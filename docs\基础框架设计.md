# Electron + Go 基础框架设计文档

## 项目概述

### 技术架构
- **前端**: Electron + Vue 3 + Vite + Element Plus
- **后端**: Go 1.24.5 + Gin
- **通信**: HTTP API
- **打包**: Electron Builder

### 架构图
```
┌─────────────────┐    HTTP API    ┌─────────────────┐
│   Electron App  │ ◄─────────────► │   Go Backend    │
│                 │                 │                 │
│ ┌─────────────┐ │                 │ ┌─────────────┐ │
│ │ Main Process│ │                 │ │   API Server│ │
│ └─────────────┘ │                 │ └─────────────┘ │
│ ┌─────────────┐ │                 └─────────────────┘
│ │Renderer Proc│ │                 
│ │(Vue 3 + UI) │ │                 
│ └─────────────┘ │                 
└─────────────────┘                 
```

## 目录结构

```
project-root/
├── electron-app/                 # Electron前端应用
│   ├── src/
│   │   ├── main/                # 主进程
│   │   │   ├── main.js
│   │   │   └── preload.js
│   │   ├── renderer/            # 渲染进程
│   │   │   ├── src/
│   │   │   │   ├── components/  # Vue组件
│   │   │   │   ├── views/       # 页面视图
│   │   │   │   ├── api/         # API调用
│   │   │   │   └── main.js      # Vue应用入口
│   │   │   ├── index.html
│   │   │   └── vite.config.js
│   │   └── resources/           # 应用资源
│   ├── package.json
│   └── electron-builder.json
├── go-backend/                   # Go后端服务
│   ├── cmd/
│   │   └── server/
│   │       └── main.go          # 服务器入口
│   ├── internal/
│   │   ├── api/                 # API路由处理
│   │   ├── service/             # 业务逻辑
│   │   └── config/              # 配置管理
│   ├── go.mod
│   └── go.sum
├── docs/                        # 项目文档
└── scripts/                     # 构建脚本
```

## 核心组件设计

### 1. Electron主进程 (Main Process)

**职责**:
- 管理应用生命周期
- 创建和管理渲染进程窗口
- 启动Go后端服务

**关键功能**:
```javascript
// 启动Go后端服务
function startGoBackend() {
  // 检测端口可用性
  // 启动Go可执行文件
  // 简单健康检查
}

// 窗口管理
function createMainWindow() {
  // 创建主窗口
  // 加载渲染进程
}
```

### 2. Electron渲染进程 (Renderer Process)

**技术栈**: Vue 3 + Vite + Element Plus

**核心模块**:
- **路由管理**: Vue Router
- **状态管理**: Pinia
- **UI组件**: Element Plus
- **HTTP客户端**: Axios

### 3. Go后端服务

**框架选择**: Gin

**核心模块**:
```go
// 服务器配置
type ServerConfig struct {
    Port string
}

// API路由组
func SetupRoutes(r *gin.Engine) {
    api := r.Group("/api/v1")
    {
        api.GET("/health", healthCheck)
        api.GET("/test", testHandler)
        // 其他业务路由
    }
}
```

## 通信机制

### HTTP API通信

**请求格式**:
```json
{
  "method": "GET",
  "url": "http://localhost:8080/api/v1/test"
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": "Hello from Go backend!"
}
```

## 开发流程

### 1. 开发环境启动
```bash
# 启动Go后端
cd go-backend
go run cmd/server/main.go

# 启动Electron前端
cd electron-app
npm run dev
```

### 2. 构建流程
```bash
# 构建Go后端
cd go-backend
go build -o ../electron-app/resources/backend cmd/server/main.go

# 构建Electron应用
cd electron-app
npm run build
npm run electron:build
```

## 开发里程碑

### Phase 1: 基础框架搭建 (1周)
- [ ] 创建项目目录结构
- [ ] 配置Electron基础环境
- [ ] 搭建Go API服务器
- [ ] 配置Vue 3 + Vite + Element Plus
- [ ] 实现前后端基础通信

### Phase 2: 核心功能开发 (按需扩展)
- [ ] 添加具体业务功能
- [ ] 完善错误处理
- [ ] 优化用户界面
- [ ] 性能调优
