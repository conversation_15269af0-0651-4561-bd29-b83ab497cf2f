<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;bd2ee144-3376-4e93-a8f6-bb71fe1b6aea&quot;,&quot;conversations&quot;:{&quot;bd2ee144-3376-4e93-a8f6-bb71fe1b6aea&quot;:{&quot;id&quot;:&quot;bd2ee144-3376-4e93-a8f6-bb71fe1b6aea&quot;,&quot;createdAtIso&quot;:&quot;2025-07-28T02:09:33.853Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-28T02:09:44.008Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;7f6a3ba9-4792-4d17-9a60-f15ae93721ab&quot;,&quot;request_message&quot;:&quot;看了下无需什么文档，现在不设计什么表结构，就先设计一个简单的架构，这个架构里的内容可以是空着的先做预留。\n然后用户权限这块我不会使用到jwt，这个项目是内网局域网项目，无需考虑这种问题&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;看了下无需什么文档，现在不设计什么表结构，就先设计一个简单的架构，这个架构里的内容可以是空着的先做预留。&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;然后用户权限这块我不会使用到jwt，这个项目是内网局域网项目，无需考虑这种问题&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;看了下无需什么文档，现在不设计什么表结构，就先设计一个简单的架构，这个架构里的内容可以是空着的先做预留。\n然后用户权限这块我不会使用到jwt，这个项目是内网局域网项目，无需考虑这种问题&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;E:/Company Projects/Yageo_489/spreader-tron-app&quot;,&quot;folderRoot&quot;:&quot;E:/Company Projects/Yageo_489/spreader-tron-app&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-28T02:09:44.017Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-13dd46e9-5fb8-4cb1-91eb-420ef79141c8&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;7ddaee4b-f693-4a5c-9665-b8ebb5a11a8b&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>